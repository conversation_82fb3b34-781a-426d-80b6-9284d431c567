import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fastifySwagger from '@fastify/swagger';
import fastifySwaggerUI from '@fastify/swagger-ui';

declare module 'fastify' {
  interface FastifySchema {
    hide?: boolean;
  }
}

// Define OpenAPI schema for authentication endpoints
const authOpenAPISchema = {
  paths: {
    '/api/auth/sign-up/email': {
      post: {
        tags: ['Authentication'],
        summary: 'Sign up with email and password',
        description: 'Create a new user account using email and password',
        operationId: 'signUpEmail',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['email', 'password', 'name'],
                properties: {
                  email: { type: 'string', format: 'email', example: '<EMAIL>' },
                  password: { type: 'string', minLength: 8, example: 'password123' },
                  name: { type: 'string', example: '<PERSON>' },
                  image: { type: 'string', format: 'uri', example: 'https://example.com/avatar.jpg' }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'User successfully created',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    user: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        name: { type: 'string' },
                        emailVerified: { type: 'boolean' }
                      }
                    },
                    session: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        expiresAt: { type: 'string', format: 'date-time' }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: 'Bad request - validation error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    code: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    },
    '/api/auth/sign-in/email': {
      post: {
        tags: ['Authentication'],
        summary: 'Sign in with email and password',
        description: 'Authenticate user with email and password',
        operationId: 'signInEmail',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['email', 'password'],
                properties: {
                  email: { type: 'string', format: 'email', example: '<EMAIL>' },
                  password: { type: 'string', example: 'password123' },
                  rememberMe: { type: 'boolean', default: true }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'User successfully authenticated',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    user: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        name: { type: 'string' },
                        emailVerified: { type: 'boolean' }
                      }
                    },
                    session: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        expiresAt: { type: 'string', format: 'date-time' }
                      }
                    }
                  }
                }
              }
            }
          },
          401: {
            description: 'Unauthorized - invalid credentials',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    code: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    },
    '/api/auth/sign-out': {
      post: {
        tags: ['Authentication'],
        summary: 'Sign out',
        description: 'Sign out the current user and invalidate session',
        operationId: 'signOut',
        security: [{ bearerAuth: [] }],
        responses: {
          200: {
            description: 'Successfully signed out',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: { type: 'boolean', example: true }
                  }
                }
              }
            }
          }
        }
      }
    },
    '/api/auth/session': {
      get: {
        tags: ['Authentication'],
        summary: 'Get current session',
        description: 'Get the current user session information',
        operationId: 'getSession',
        security: [{ bearerAuth: [] }],
        responses: {
          200: {
            description: 'Current session information',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    user: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        name: { type: 'string' },
                        emailVerified: { type: 'boolean' }
                      }
                    },
                    session: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        expiresAt: { type: 'string', format: 'date-time' }
                      }
                    }
                  }
                }
              }
            }
          },
          401: {
            description: 'Unauthorized - no valid session',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    code: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};

export async function registerSwagger(app: FastifyInstance): Promise<void> {
  try {
    // Register Swagger
    await app.register(fastifySwagger, {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'Life Navigation API',
          description: 'API documentation for Life Navigation application',
          version: '1.0.0',
        },
        servers: [
          { url: 'http://localhost:3002', description: 'Development' },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        paths: {
          ...authOpenAPISchema.paths,
        },
        tags: [
          { name: 'Authentication', description: 'Authentication endpoints' },
          { name: 'Health', description: 'Health check endpoints' }
        ]
      },
      hideUntagged: false // Show all routes, even those without tags
    });

    // Register Swagger UI
    await app.register(fastifySwaggerUI, {
      routePrefix: '/documentation',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
      },
      staticCSP: true,
      transformSpecification: (swaggerObject: any) => {
        // Ensure the base path is correct
        swaggerObject.servers = [{ url: '/' }];
        return swaggerObject;
      },
    });

    // Add a redirect from /docs to /documentation
    app.get('/docs', async (request: FastifyRequest, reply: FastifyReply) => {
      return reply.redirect('/documentation/');
    });

    app.log.info('Swagger documentation is available at /documentation');
  } catch (error) {
    app.log.error('Failed to initialize Swagger:', error);
    throw error;
  }
}
