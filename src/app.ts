import fastify, { FastifyInstance, FastifyError, FastifyReply, FastifyRequest } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import { Headers, Request } from 'node-fetch';
import envPlugin from './config/env';
import { registerSwagger } from './config/swagger';
import { auth } from './lib/auth';
import { userRoutes } from './routes/users';
import { InitService } from './services/initService';

export async function createApp(): Promise<FastifyInstance> {
  const app = fastify({
    logger: {
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname',
        },
      },
    },
    disableRequestLogging: process.env.NODE_ENV === 'production',
    trustProxy: true, // Trust proxy headers for secure cookies
  });

  // Register environment variables first
  await app.register(envPlugin);


  // Configure security headers first
  await app.register(helmet, {
    contentSecurityPolicy: false, // Disable CSP for now to avoid conflicts
    crossOriginEmbedderPolicy: false, // Required for some auth flows
    crossOriginOpenerPolicy: { policy: 'same-origin' },
    crossOriginResourcePolicy: { policy: 'same-site' },
  });

  // Configure CORS with settings from environment
  const corsOptions = {
    origin: app.config.NODE_ENV === 'production'
      ? app.config.CORS_ORIGIN?.split(',').map(origin => origin.trim())
      : '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['Content-Range', 'X-Total-Count'],
    credentials: true,
    maxAge: 600, // 10 minutes
  };

  // Register CORS with the provided options
  await app.register(cors, corsOptions);

  // Register Swagger before defining routes
  await registerSwagger(app);

  // Initialize default accounts
  await InitService.initializeDefaultAccounts(app);

  // Register user routes
  await app.register(userRoutes, { prefix: '/api/users' });

  // Authentication routes
  app.route({
    method: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    url: '/api/auth/*',
    handler: async (request, reply) => {
      try {
        // Construct request URL
        const url = new URL(request.url, `http://${request.headers.host}`);

        // Convert Fastify headers to standard Headers object
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (value) headers.append(key, value.toString());
        });

        // Create a Fetch API compatible request
        const req = new Request(url.toString(), {
          method: request.method,
          headers,
          body: request.body && request.method !== 'GET' && request.method !== 'HEAD'
            ? JSON.stringify(request.body)
            : undefined,
        });

        app.log.info(`Forwarding request to auth handler for URL: ${req.url}`);
        // Handle the request with auth
        const response = await auth.handler(req as any);

        // Copy response headers
        const responseHeaders: Record<string, string> = {};
        (response.headers as any).forEach((value: string, key: string) => {
          responseHeaders[key] = value;
        });

        // Set response status and headers
        reply.status(response.status);
        Object.entries(responseHeaders).forEach(([key, value]) => {
          if (value) {
            reply.header(key, value);
          }
        });

        // Handle different response types
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json();
          reply.send(data);
        } else {
          const text = await response.text();
          reply.send(text);
        }

      } catch (error) {
        app.log.error(error, 'Error in auth handler');
        reply.status(500).send({ error: 'Internal server error' });
      }
    },
  });

  // Health check route with OpenAPI documentation
  app.get(
    '/health',
    {
      schema: {
        tags: ['Health'],
        summary: 'Health Check',
        description: 'Check the health status of the API server',
        operationId: 'healthCheck',
        security: [], // No auth required for health check
        response: {
          200: {
            description: 'Successful health check response',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: {
                      type: 'string',
                      enum: ['ok'],
                      description: 'Service status',
                      example: 'ok'
                    },
                    timestamp: {
                      type: 'string',
                      format: 'date-time',
                      description: 'Current server time',
                      example: '2025-06-29T13:15:05.000Z'
                    },
                    environment: {
                      type: 'string',
                      description: 'Current environment',
                      example: 'development'
                    },
                  },
                  required: ['status', 'timestamp', 'environment']
                }
              }
            }
          },
          500: {
            description: 'Server Error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    statusCode: { type: 'number' },
                    error: { type: 'string' },
                    message: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        openapi: {
          tags: [{ name: 'Health' }]
        }
      } as const,
    },
    async () => {
      return {
        status: 'ok' as const,
        timestamp: new Date().toISOString(),
        environment: app.config.NODE_ENV,
      };
    },
  );

  // Example protected route
  app.get('/protected', async (_request, _reply) => {
    // Note: The session logic will be added here in the next step.
    return { message: 'This is a protected route, but still needs session validation.' };
  });

  // Error handling
  app.setErrorHandler((error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
    const statusCode = error.statusCode || 500;

    // Log the error
    request.log.error({
      req: request,
      res: reply,
      err: error,
      msg: error.message
    }, 'Request error');

    // Send error response
    reply.status(statusCode).send({
      statusCode,
      error: statusCode === 500 ? 'Internal Server Error' : error.name,
      message: statusCode === 500 ? 'An internal server error occurred' : error.message
    });
  });

  // Add type for rawBody
  app.addHook('preValidation', (request: FastifyRequest, _reply: FastifyReply, done: (err?: Error) => void) => {
    (request as any).rawBody = request.body;
    done();
  });



  // Handle 404 - Keep this as the last route
  app.setNotFoundHandler((request, reply) => {
    reply.status(404).send({
      statusCode: 404,
      error: 'Not Found',
      message: `Route ${request.method}:${request.url} not found`
    });
  });

  return app;
}