import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FastifyInstance } from 'fastify';
import { createApp } from '../../src/app';
import { UserService } from '../../src/services/userService';
import { testPrisma } from '../setup';

describe('User Routes', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    // Create a fresh app instance for each test
    app = await createApp();
    await app.ready();

    // Clean up database
    await testPrisma.user.deleteMany();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /api/users/:id', () => {
    it('should get user by ID', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      const response = await app.inject({
        method: 'GET',
        url: `/api/users/${user.id}`,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        id: user.id,
        email: user.email,
        name: user.name,
      });
    });

    it('should return 404 for non-existent user', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users/non-existent-id',
      });

      expect(response.statusCode).toBe(404);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        error: 'Not Found',
        message: 'User not found',
      });
    });
  });

  describe('GET /api/users', () => {
    it('should get all users with pagination', async () => {
      // Ensure clean state
      await testPrisma.user.deleteMany();

      // Create test users
      await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'User 1',
      });
      await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'User 2',
      });
      await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'User 3',
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/users?page=1&limit=2',
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.users).toHaveLength(2);
      expect(responseBody.total).toBe(3);
      expect(responseBody.page).toBe(1);
      expect(responseBody.limit).toBe(2);
    });

    it('should return empty array when no users exist', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users',
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.users).toHaveLength(0);
      expect(responseBody.total).toBe(0);
    });
  });

  describe('PATCH /api/users/:id/status', () => {
    it('should toggle user status', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/status`,
        payload: {
          disabled: true,
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.disabled).toBe(true);

      // Verify in database
      const updatedUser = await UserService.findById(user.id);
      expect(updatedUser?.disabled).toBe(true);
    });

    it('should enable disabled user', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      // First disable the user
      await UserService.toggleUserStatus(user.id, true);

      // Then enable the user
      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/status`,
        payload: {
          disabled: false,
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.disabled).toBe(false);
    });
  });

  describe('PATCH /api/users/:id/role', () => {
    it('should update user role to admin', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/role`,
        payload: {
          role: 'admin',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.role).toBe('admin');

      // Verify in database
      const updatedUser = await UserService.findById(user.id);
      expect(updatedUser?.role).toBe('admin');
    });

    it('should update admin role to user', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Admin User',
        role: 'admin',
      });

      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/role`,
        payload: {
          role: 'user',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.role).toBe('user');
    });
  });

  describe('GET /api/users/profile', () => {
    it('should return 501 for profile endpoint (not implemented)', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users/profile',
      });

      expect(response.statusCode).toBe(501);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        error: 'Not implemented',
        message: 'Session validation not yet implemented',
      });
    });
  });

  describe('PUT /api/users/profile', () => {
    it('should return 501 for profile update endpoint (not implemented)', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: '/api/users/profile',
        payload: {
          name: 'Updated Name',
        },
      });

      expect(response.statusCode).toBe(501);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        error: 'Not implemented',
        message: 'Session validation not yet implemented',
      });
    });
  });
});
