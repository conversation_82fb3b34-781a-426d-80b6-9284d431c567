import { beforeAll, afterAll, beforeEach } from 'vitest';
import { PrismaClient } from '@prisma/client';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/rsdh_test';

// Test database instance
export const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

beforeAll(async () => {
  // Connect to test database
  await testPrisma.$connect();

  // Clean up database at start
  await cleanDatabase();
});

afterAll(async () => {
  // Clean up database at end
  await cleanDatabase();

  // Disconnect from test database
  await testPrisma.$disconnect();
});

beforeEach(async () => {
  // Clean up database before each test
  await cleanDatabase();
});

async function cleanDatabase() {
  // Delete in correct order to avoid foreign key constraints
  await testPrisma.passwordResetToken.deleteMany();
  await testPrisma.refreshToken.deleteMany();
  await testPrisma.session.deleteMany();
  await testPrisma.user.deleteMany();
}
