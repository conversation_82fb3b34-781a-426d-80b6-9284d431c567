import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FastifyInstance } from 'fastify';
import { createApp } from '../src/app';
import { testPrisma } from './setup';

describe('App', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    // Clean up database
    await testPrisma.user.deleteMany();
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('Application Startup', () => {
    it('should create app successfully', async () => {
      // Set test environment variables
      process.env.ADMIN_EMAIL = '<EMAIL>';
      process.env.ADMIN_PASSWORD = 'testpassword123';

      app = await createApp();
      await app.ready();

      expect(app).toBeDefined();
      expect(app.config).toBeDefined();
    });

    it('should initialize default accounts on startup', async () => {
      // Ensure clean state
      await testPrisma.user.deleteMany();

      // Set test environment variables
      process.env.ADMIN_EMAIL = '<EMAIL>';
      process.env.ADMIN_PASSWORD = 'testpassword123';

      app = await createApp();
      await app.ready();

      // Check if admin account was created
      const adminEmail = app.config.ADMIN_EMAIL;
      if (adminEmail) {
        const adminUser = await testPrisma.user.findUnique({
          where: { email: adminEmail },
        });
        expect(adminUser).toBeDefined();
        expect(adminUser?.role).toBe('admin');
      }
    });
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      app = await createApp();
      await app.ready();

      const response = await app.inject({
        method: 'GET',
        url: '/health',
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        status: 'ok',
        environment: expect.any(String),
        timestamp: expect.any(String),
      });
    });
  });

  describe('CORS', () => {
    it('should handle CORS preflight requests', async () => {
      app = await createApp();
      await app.ready();

      const response = await app.inject({
        method: 'OPTIONS',
        url: '/api/users',
        headers: {
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type',
          Origin: 'http://localhost:3000',
        },
      });

      expect(response.statusCode).toBe(204);
      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for non-existent routes', async () => {
      app = await createApp();
      await app.ready();

      const response = await app.inject({
        method: 'GET',
        url: '/non-existent-route',
      });

      expect(response.statusCode).toBe(404);
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        statusCode: 404,
        error: 'Not Found',
        message: expect.stringContaining('Route GET:/non-existent-route not found'),
      });
    });
  });

  describe('Protected Route', () => {
    it('should access protected route (placeholder)', async () => {
      app = await createApp();
      await app.ready();

      const response = await app.inject({
        method: 'GET',
        url: '/protected',
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        message: 'This is a protected route, but still needs session validation.',
      });
    });
  });

  describe('Swagger Documentation', () => {
    it('should serve Swagger UI', async () => {
      app = await createApp();
      await app.ready();

      const response = await app.inject({
        method: 'GET',
        url: '/documentation',
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
    });

    it('should serve OpenAPI JSON', async () => {
      app = await createApp();
      await app.ready();

      const response = await app.inject({
        method: 'GET',
        url: '/documentation/json',
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('application/json');

      const openApiSpec = JSON.parse(response.body);
      expect(openApiSpec.openapi).toBe('3.0.0');
      expect(openApiSpec.info.title).toBe('Life Navigation API');
    });
  });
});
